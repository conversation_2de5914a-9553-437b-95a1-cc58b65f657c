'use client';

import { useRef } from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'chart.js';
import { Radar } from 'react-chartjs-2';
import { OceanScores } from '@/lib/types';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

interface OceanRadarChartProps {
  scores: OceanScores;
}

export default function OceanRadarChart({ scores }: OceanRadarChartProps) {
  const chartRef = useRef<ChartJS<'radar'>>(null);

  const data = {
    labels: [
      'Openness',
      'Conscientiousness', 
      'Extraversion',
      'Agreeableness',
      'Neuroticism'
    ],
    datasets: [
      {
        label: 'Skor OCEAN Anda',
        data: [scores.O, scores.C, scores.E, scores.A, scores.N],
        backgroundColor: 'rgba(34, 197, 94, 0.2)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 3,
        pointBackgroundColor: 'rgba(34, 197, 94, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          font: {
            size: 14,
            weight: 'bold' as const,
          },
          color: '#374151',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
        callbacks: {
          label: function(context: { parsed: { r: number } }) {
            // Convert score to trait level
            const score = context.parsed.r;
            let level = 'Rendah';
            if (score >= 20) level = 'Tinggi';
            else if (score >= 15) level = 'Sedang';

            return [`Skor: ${score}`, `Tingkat Trait: ${level}`];
          }
        }
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        min: 0,
        max: 25, // Maximum possible score (5 questions × 5 points each)
        ticks: {
          display: true, // Show the numbers on the radial scale
          stepSize: 5,
          font: {
            size: 10,
          },
          color: '#6B7280',
        },
        grid: {
          color: 'rgba(156, 163, 175, 0.3)',
        },
        angleLines: {
          color: 'rgba(156, 163, 175, 0.3)',
        },
        pointLabels: {
          font: {
            size: 13,
            weight: 'bold' as const,
          },
          color: '#374151',
        },
      },
    },
  };

  return (
    <div className="w-full h-96 relative">
      <Radar ref={chartRef} data={data} options={options} />
    </div>
  );
}
