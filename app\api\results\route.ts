import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse
} from '@/lib/api-utils';
import { ResultsResponse } from '@/lib/api-types';
import { ServerGeminiProfileService } from '@/lib/server/geminiService';
import { RiasecScores, OceanScores } from '@/lib/types';
import { db } from '@/lib/server/database';
import { withMiddleware } from '@/lib/server/middleware';

async function getHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  // Get scores from query parameters with validation
  const r = parseInt(searchParams.get('r') || '0');
  const i = parseInt(searchParams.get('i') || '0');
  const a = parseInt(searchParams.get('a') || '0');
  const s = parseInt(searchParams.get('s') || '0');
  const e = parseInt(searchParams.get('e') || '0');
  const c = parseInt(searchParams.get('c') || '0');

  const o = parseInt(searchParams.get('o') || '0');
  const ocean_c = parseInt(searchParams.get('ocean_c') || '0');
  const ocean_e = parseInt(searchParams.get('ocean_e') || '0');
  const ocean_a = parseInt(searchParams.get('ocean_a') || '0');
  const n = parseInt(searchParams.get('n') || '0');

  // Validate RIASEC scores (each should be between 0-30 for 6 questions with 1-5 scale)
  if ([r, i, a, s, e, c].some(score => score < 0 || score > 30 || isNaN(score))) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid RIASEC scores. Each score must be between 0-30',
      400
    );
  }

  // Validate OCEAN scores (each should be between 5-25 for 5 questions with 1-5 scale)
  if ([o, ocean_c, ocean_e, ocean_a, n].some(score => score < 5 || score > 25 || isNaN(score))) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid OCEAN scores. Each score must be between 5-25',
      400
    );
  }

  const riasecScores: RiasecScores = { R: r, I: i, A: a, S: s, E: e, C: c };
  const oceanScores: OceanScores = { O: o, C: ocean_c, E: ocean_e, A: ocean_a, N: n };

  // Create assessment in database
  const dbAssessment = await db.createAssessment(riasecScores, oceanScores);

  // Generate profile using Gemini AI with timeout
  const geminiService = ServerGeminiProfileService.getInstance();

  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Profile generation timeout')), 45000);
  });

  const aiProfile = await Promise.race([
    geminiService.generateCombinedProfile(riasecScores, oceanScores),
    timeoutPromise
  ]);

  // Save profile to database
  await db.createProfile(dbAssessment.id, aiProfile as any);

  // Mark assessment as having profile generated
  await db.markProfileGenerated(dbAssessment.id);

  const assessment = {
    id: dbAssessment.id,
    riasecScores,
    oceanScores,
    createdAt: dbAssessment.createdAt.toISOString(),
    profileGenerated: true,
  };

  const profile = {
    ...aiProfile as any,
    generatedAt: new Date().toISOString(),
  };

  const response: ResultsResponse = {
    assessment,
    profile,
  };

  return createSuccessResponse(response);
}

export const GET = withMiddleware(getHandler);
