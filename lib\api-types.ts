// API Request/Response Types
import { RiasecScores, OceanScores } from './types';

// Assessment API Types
export interface AssessmentRequest {
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
  userId?: string; // Optional for future user management
}

export interface AssessmentResponse {
  id: string;
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
  createdAt: string;
  profileGenerated: boolean;
}

// Profile Generation API Types
export interface ProfileGenerationRequest {
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
}

export interface ProfileGenerationResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workEnvironment: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
  generatedAt: string;
}

// Results API Types
export interface ResultsRequest {
  assessmentId: string;
}

export interface ResultsResponse {
  assessment: AssessmentResponse;
  profile: ProfileGenerationResponse;
}

// Error Response Type
export interface ApiErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// Success Response Wrapper
export interface ApiSuccessResponse<T> {
  success: true;
  data: T;
  timestamp: string;
}

// Generic API Response
export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;
