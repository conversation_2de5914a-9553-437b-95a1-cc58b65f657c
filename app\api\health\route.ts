import { NextRequest } from 'next/server';
import { createSuccessResponse } from '@/lib/api-utils';
import { withMiddleware, healthCheck } from '@/lib/server/middleware';

async function handler(req: NextRequest) {
  const health = await healthCheck();

  return createSuccessResponse({
    status: 'ok',
    message: 'API is running',
    timestamp: new Date().toISOString(),
    services: health,
  });
}

export const GET = withMiddleware(handler);
