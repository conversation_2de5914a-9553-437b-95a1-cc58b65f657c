interface NavigationButtonsProps {
  currentIndex: number;
  totalQuestions: number;
  canGoNext: boolean;
  onPrevious: () => void;
  onNext: () => void;
}

export default function NavigationButtons({
  currentIndex,
  totalQuestions,
  canGoNext,
  onPrevious,
  onNext
}: NavigationButtonsProps) {
  const isFirstQuestion = currentIndex === 0;
  const isLastQuestion = currentIndex === totalQuestions - 1;

  return (
    <div className="flex justify-between items-center mt-8">
      <button
        onClick={onPrevious}
        disabled={isFirstQuestion}
        className={`
          px-6 py-3 rounded-lg font-medium transition-all duration-200
          ${isFirstQuestion
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }
        `}
      >
        ← Kembali
      </button>

      <div className="text-sm text-gray-500">
      </div>

      {canGoNext && (
        <button
          onClick={onNext}
          className={`
            px-6 py-3 rounded-lg font-medium transition-all duration-300
            ${isLastQuestion
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
            }
          `}
        >
          {isLastQuestion ? 'Selesai →' : 'Lanjut →'}
        </button>
      )}

      {!canGoNext && (
        <div className="w-24"></div> // Spacer to maintain layout
      )}
    </div>
  );
}
