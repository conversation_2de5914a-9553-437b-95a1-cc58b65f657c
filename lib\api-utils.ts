import { NextResponse } from 'next/server';
import { ApiErrorResponse, ApiSuccessResponse } from './api-types';

// Helper function to create success response
export function createSuccessResponse<T>(data: T): NextResponse<ApiSuccessResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    timestamp: new Date().toISOString(),
  });
}

// Helper function to create error response
export function createErrorResponse(
  error: string,
  message: string,
  statusCode: number = 500
): NextResponse<ApiErrorResponse> {
  return NextResponse.json(
    {
      error,
      message,
      statusCode,
      timestamp: new Date().toISOString(),
    },
    { status: statusCode }
  );
}

// Helper function to validate required fields
export function validateRequiredFields(
  data: Record<string, any>,
  requiredFields: string[]
): string | null {
  for (const field of requiredFields) {
    if (!data[field] && data[field] !== 0) {
      return `Missing required field: ${field}`;
    }
  }
  return null;
}

// Helper function to generate unique ID
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Helper function to validate RIASEC scores
export function validateRiasecScores(scores: any): boolean {
  if (!scores || typeof scores !== 'object') return false;
  
  const requiredKeys = ['R', 'I', 'A', 'S', 'E', 'C'];
  for (const key of requiredKeys) {
    const score = scores[key];
    if (typeof score !== 'number' || score < 0 || score > 30) {
      return false;
    }
  }
  return true;
}

// Helper function to validate OCEAN scores
export function validateOceanScores(scores: any): boolean {
  if (!scores || typeof scores !== 'object') return false;
  
  const requiredKeys = ['O', 'C', 'E', 'A', 'N'];
  for (const key of requiredKeys) {
    const score = scores[key];
    if (typeof score !== 'number' || score < 5 || score > 25) {
      return false;
    }
  }
  return true;
}
