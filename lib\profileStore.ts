import { RiasecType, RiasecScores, OceanScores, OceanType } from './types';
import { oceanQuestions } from './oceanQuestions';



// Interface untuk profil interpretasi gabungan RIASEC + OCEAN
export interface CombinedProfileInterpretation {
  // RIASEC data
  riasecData: {
    scores: RiasecScores;
    dominantTypes: RiasecType[];
    level: string;
  };

  // OCEAN data
  oceanData: {
    scores: OceanScores;
    traits: OceanTraitSummary[];
    personalityType: string;
  };

  // Combined analysis
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workEnvironment: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}

// Interface untuk ringkasan trait OCEAN
export interface OceanTraitSummary {
  trait: OceanType;
  name: string;
  score: number;
  level: 'Sangat Rendah' | 'Rendah' | 'Sedang' | 'Tinggi' | 'Sangat Tinggi';
  description: string;
}



// Import standar visualisasi tingkat
import { getRiasecScoreLevel, getOceanScoreLevel } from './levelVisualization';

// Fungsi untuk mendapatkan level interpretasi skor RIASEC (backward compatibility)
export function getScoreLevel(score: number): { level: string; color: string; description: string } {
  return getRiasecScoreLevel(score);
}



// Helper functions untuk OCEAN trait analysis
export function getOceanTraitLevel(score: number): 'Sangat Rendah' | 'Rendah' | 'Sedang' | 'Tinggi' | 'Sangat Tinggi' {
  return getOceanScoreLevel(score).level;
}

export function getOceanTraitDescription(trait: string, level: 'Sangat Rendah' | 'Rendah' | 'Sedang' | 'Tinggi' | 'Sangat Tinggi'): string {
  // Support both short keys (O, C, E, A, N) and full names
  const traitKey = trait.length === 1 ? trait : trait.charAt(0);

  const descriptions = {
    'O': {
      'Sangat Rendah': 'Sangat konvensional, menghindari perubahan dan ide baru',
      'Rendah': 'Cenderung praktis dan konvensional dalam pendekatan',
      'Sedang': 'Seimbang antara praktis dan terbuka terhadap ide baru',
      'Tinggi': 'Sangat kreatif, imajinatif, dan terbuka terhadap pengalaman baru',
      'Sangat Tinggi': 'Ekstrem kreatif, visioner, dan selalu mencari pengalaman baru'
    },
    'C': {
      'Sangat Rendah': 'Sangat tidak terorganisir dan impulsif',
      'Rendah': 'Cenderung fleksibel dan spontan dalam pendekatan',
      'Sedang': 'Seimbang antara terorganisir dan fleksibel',
      'Tinggi': 'Sangat terorganisir, disiplin, dan berorientasi pada tujuan',
      'Sangat Tinggi': 'Ekstrem perfeksionis, sangat disiplin dan terstruktur'
    },
    'E': {
      'Sangat Rendah': 'Sangat introvert, menghindari interaksi sosial',
      'Rendah': 'Cenderung introspektif dan menyukai ketenangan',
      'Sedang': 'Seimbang antara sosial dan waktu pribadi',
      'Tinggi': 'Sangat sosial, energik, dan mencari stimulasi eksternal',
      'Sangat Tinggi': 'Ekstrem ekstrovert, selalu mencari perhatian dan stimulasi'
    },
    'A': {
      'Sangat Rendah': 'Sangat kompetitif, skeptis, dan sulit percaya',
      'Rendah': 'Cenderung kompetitif dan skeptis',
      'Sedang': 'Seimbang antara kooperatif dan asertif',
      'Tinggi': 'Sangat kooperatif, empati, dan mudah percaya',
      'Sangat Tinggi': 'Ekstrem altruistik, sangat empati dan mudah dimanfaatkan'
    },
    'N': {
      'Sangat Rendah': 'Ekstrem stabil, tidak pernah cemas atau stres',
      'Rendah': 'Sangat stabil secara emosional dan tenang',
      'Sedang': 'Cukup stabil dengan sesekali mengalami stres',
      'Tinggi': 'Sensitif terhadap stres dan emosi negatif',
      'Sangat Tinggi': 'Sangat tidak stabil emosional, mudah cemas dan depresi'
    }
  };

  return descriptions[traitKey as keyof typeof descriptions]?.[level] || 'Deskripsi tidak tersedia';
}





// Function to calculate OCEAN scores from answers
export function calculateOceanScores(answers: Record<number, number>): OceanScores {
  const scores: OceanScores = { O: 0, C: 0, E: 0, A: 0, N: 0 };

  oceanQuestions.forEach(question => {
    const answer = answers[question.id];
    if (answer !== undefined) {
      // For reversed questions, flip the score (1->5, 2->4, 3->3, 4->2, 5->1)
      const adjustedScore = question.isReversed ? (6 - answer) : answer;
      scores[question.ocean_type] += adjustedScore;
    }
  });

  return scores;
}
