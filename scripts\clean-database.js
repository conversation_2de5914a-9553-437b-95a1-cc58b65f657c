const { PrismaClient } = require('../lib/generated/prisma');
const readline = require('readline');

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase().trim());
    });
  });
}

async function showDatabaseStats() {
  console.log('\n📊 Current Database Statistics:');
  console.log('================================');
  
  try {
    const userCount = await prisma.user.count();
    const assessmentCount = await prisma.assessment.count();
    const profileCount = await prisma.profile.count();
    
    console.log(`👥 Users: ${userCount}`);
    console.log(`📝 Assessments: ${assessmentCount}`);
    console.log(`🎯 Profiles: ${profileCount}`);
    
    // Show recent assessments
    if (assessmentCount > 0) {
      console.log('\n📅 Recent Assessments:');
      const recentAssessments = await prisma.assessment.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: { profile: true }
      });
      
      recentAssessments.forEach((assessment, index) => {
        const hasProfile = assessment.profile ? '✅' : '❌';
        console.log(`  ${index + 1}. ${assessment.id} (${assessment.createdAt.toLocaleDateString()}) ${hasProfile}`);
      });
    }
    
    return { userCount, assessmentCount, profileCount };
  } catch (error) {
    console.error('❌ Error getting database stats:', error.message);
    return null;
  }
}

async function cleanProfiles() {
  console.log('\n🧹 Cleaning Profiles...');
  const result = await prisma.profile.deleteMany({});
  console.log(`✅ Deleted ${result.count} profiles`);
  return result.count;
}

async function cleanAssessments() {
  console.log('\n🧹 Cleaning Assessments...');
  const result = await prisma.assessment.deleteMany({});
  console.log(`✅ Deleted ${result.count} assessments`);
  return result.count;
}

async function cleanUsers() {
  console.log('\n🧹 Cleaning Users...');
  const result = await prisma.user.deleteMany({});
  console.log(`✅ Deleted ${result.count} users`);
  return result.count;
}

async function cleanOldAssessments(days = 30) {
  console.log(`\n🧹 Cleaning Assessments older than ${days} days...`);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  const result = await prisma.assessment.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate
      }
    }
  });
  
  console.log(`✅ Deleted ${result.count} old assessments`);
  return result.count;
}

async function cleanOrphanedProfiles() {
  console.log('\n🧹 Cleaning Orphaned Profiles...');
  
  // Find profiles without assessments
  const orphanedProfiles = await prisma.profile.findMany({
    where: {
      assessment: null
    }
  });
  
  if (orphanedProfiles.length > 0) {
    const result = await prisma.profile.deleteMany({
      where: {
        id: {
          in: orphanedProfiles.map(p => p.id)
        }
      }
    });
    console.log(`✅ Deleted ${result.count} orphaned profiles`);
    return result.count;
  } else {
    console.log('✅ No orphaned profiles found');
    return 0;
  }
}

async function interactiveClean() {
  console.log('🧹 Database Cleaning Tool');
  console.log('========================\n');
  
  const stats = await showDatabaseStats();
  if (!stats) return;
  
  if (stats.assessmentCount === 0 && stats.profileCount === 0 && stats.userCount === 0) {
    console.log('\n✨ Database is already clean!');
    return;
  }
  
  console.log('\n🔧 Cleaning Options:');
  console.log('1. Clean everything (DANGER: All data will be lost)');
  console.log('2. Clean only profiles');
  console.log('3. Clean only assessments (will also delete profiles)');
  console.log('4. Clean only users');
  console.log('5. Clean old assessments (older than 30 days)');
  console.log('6. Clean orphaned profiles');
  console.log('7. Show stats only');
  console.log('0. Exit');
  
  const choice = await askQuestion('\nChoose an option (0-7): ');
  
  switch (choice) {
    case '1':
      const confirmAll = await askQuestion('⚠️  Are you SURE you want to delete ALL data? Type "yes" to confirm: ');
      if (confirmAll === 'yes') {
        await cleanProfiles();
        await cleanAssessments();
        await cleanUsers();
        console.log('\n🗑️ All data has been deleted!');
      } else {
        console.log('❌ Operation cancelled');
      }
      break;
      
    case '2':
      const confirmProfiles = await askQuestion('Delete all profiles? (y/n): ');
      if (confirmProfiles === 'y' || confirmProfiles === 'yes') {
        await cleanProfiles();
      }
      break;
      
    case '3':
      const confirmAssessments = await askQuestion('Delete all assessments and profiles? (y/n): ');
      if (confirmAssessments === 'y' || confirmAssessments === 'yes') {
        await cleanProfiles();
        await cleanAssessments();
      }
      break;
      
    case '4':
      const confirmUsers = await askQuestion('Delete all users? (y/n): ');
      if (confirmUsers === 'y' || confirmUsers === 'yes') {
        await cleanUsers();
      }
      break;
      
    case '5':
      const days = await askQuestion('Delete assessments older than how many days? (default: 30): ');
      const daysNum = parseInt(days) || 30;
      await cleanOldAssessments(daysNum);
      break;
      
    case '6':
      await cleanOrphanedProfiles();
      break;
      
    case '7':
      // Stats already shown
      break;
      
    case '0':
      console.log('👋 Goodbye!');
      break;
      
    default:
      console.log('❌ Invalid option');
  }
}

async function main() {
  try {
    await interactiveClean();
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
    rl.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { 
  cleanProfiles, 
  cleanAssessments, 
  cleanUsers, 
  cleanOldAssessments, 
  cleanOrphanedProfiles,
  showDatabaseStats 
};
