const https = require('http');

const BASE_URL = 'http://localhost:3000/api';

// Test data
const testAssessment = {
  riasecScores: {
    R: 15,
    I: 20,
    A: 12,
    S: 18,
    E: 16,
    C: 14
  },
  oceanScores: {
    O: 18,
    C: 16,
    E: 20,
    A: 17,
    N: 12
  }
};

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Test functions
async function testHealthEndpoint() {
  console.log('🔍 Testing Health Endpoint...');
  try {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const response = await makeRequest(options);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Health endpoint working');
      console.log('   Database:', response.data.data.services.database ? '✅' : '❌');
      console.log('   Gemini:', response.data.data.services.gemini ? '✅' : '❌');
    } else {
      console.log('❌ Health endpoint failed');
      console.log('   Status:', response.status);
    }
  } catch (error) {
    console.log('❌ Health endpoint error:', error.message);
  }
  console.log('');
}

async function testAssessmentEndpoint() {
  console.log('🔍 Testing Assessment Endpoint...');
  try {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/assessments',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const response = await makeRequest(options, testAssessment);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Assessment creation working');
      console.log('   Assessment ID:', response.data.data.id);
      return response.data.data.id;
    } else {
      console.log('❌ Assessment creation failed');
      console.log('   Status:', response.status);
      console.log('   Error:', response.data.error || response.data);
    }
  } catch (error) {
    console.log('❌ Assessment endpoint error:', error.message);
  }
  console.log('');
  return null;
}

async function testGetAssessment(assessmentId) {
  if (!assessmentId) return;
  
  console.log('🔍 Testing Get Assessment Endpoint...');
  try {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/assessments?id=${assessmentId}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const response = await makeRequest(options);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Get assessment working');
      console.log('   Profile generated:', response.data.data.profileGenerated ? '✅' : '❌');
    } else {
      console.log('❌ Get assessment failed');
      console.log('   Status:', response.status);
    }
  } catch (error) {
    console.log('❌ Get assessment error:', error.message);
  }
  console.log('');
}

async function testProfileGeneration() {
  console.log('🔍 Testing Profile Generation Endpoint...');
  try {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/profiles/generate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    console.log('   Generating profile (this may take 10-30 seconds)...');
    const response = await makeRequest(options, testAssessment);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Profile generation working');
      console.log('   Profile title:', response.data.data.profileTitle);
      console.log('   Strengths count:', response.data.data.strengths.length);
      console.log('   Career suggestions count:', response.data.data.careerSuggestions.length);
    } else {
      console.log('❌ Profile generation failed');
      console.log('   Status:', response.status);
      console.log('   Error:', response.data.error || response.data);
    }
  } catch (error) {
    console.log('❌ Profile generation error:', error.message);
  }
  console.log('');
}

async function testResultsEndpoint() {
  console.log('🔍 Testing Results Endpoint...');
  try {
    const params = new URLSearchParams({
      r: testAssessment.riasecScores.R.toString(),
      i: testAssessment.riasecScores.I.toString(),
      a: testAssessment.riasecScores.A.toString(),
      s: testAssessment.riasecScores.S.toString(),
      e: testAssessment.riasecScores.E.toString(),
      c: testAssessment.riasecScores.C.toString(),
      o: testAssessment.oceanScores.O.toString(),
      ocean_c: testAssessment.oceanScores.C.toString(),
      ocean_e: testAssessment.oceanScores.E.toString(),
      ocean_a: testAssessment.oceanScores.A.toString(),
      n: testAssessment.oceanScores.N.toString(),
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/results?${params.toString()}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    console.log('   Getting results with profile generation (this may take 10-30 seconds)...');
    const response = await makeRequest(options);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Results endpoint working');
      console.log('   Assessment created:', response.data.data.assessment.id);
      console.log('   Profile generated:', response.data.data.profile.profileTitle);
    } else {
      console.log('❌ Results endpoint failed');
      console.log('   Status:', response.status);
      console.log('   Error:', response.data.error || response.data);
    }
  } catch (error) {
    console.log('❌ Results endpoint error:', error.message);
  }
  console.log('');
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  // Test health endpoint
  await testHealthEndpoint();
  
  // Test assessment creation
  const assessmentId = await testAssessmentEndpoint();
  
  // Test get assessment
  await testGetAssessment(assessmentId);
  
  // Test profile generation
  await testProfileGeneration();
  
  // Test results endpoint (full integration)
  await testResultsEndpoint();
  
  console.log('✅ All tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
