import { Question, OceanQuestion } from '@/lib/types';

interface QuestionCardProps {
  question: Question | OceanQuestion;
  questionNumber: number;
  totalQuestions: number;
}

export default function QuestionCard({ question, questionNumber, totalQuestions }: QuestionCardProps) {
  return (
    <div className="bg-white rounded-xl shadow-lg p-8 mb-8 w-full max-w-4xl mx-auto">
      <div className="mb-6 min-h-[120px] flex items-center">
        <h2 className="text-xl md:text-2xl font-semibold text-gray-800 leading-relaxed w-full">
          {question.text}
        </h2>
      </div>
    </div>
  );
}
