'use client';

import { LEVEL_VISUALIZATIONS, LevelType, getRiasecScoreLevel, getOceanScoreLevel } from '@/lib/levelVisualization';

// Komponen demo untuk menunjukkan standar visualisasi tingkat
export default function LevelVisualizationDemo() {
  const levels: LevelType[] = ['Sangat Rendah', 'Rendah', 'Sedang', 'Tinggi', 'Sangat Tinggi'];
  
  // Contoh skor untuk demo
  const riasecScores = [2, 7, 12, 17, 22];
  const oceanScores = [6, 10, 15, 20, 24];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Standar Visualisasi Tingkat
        </h1>
        <p className="text-gray-600">
          Konsistensi icon, warna, dan deskripsi untuk komponen RIASEC dan <PERSON>
        </p>
      </div>

      {/* Standar Umum */}
      <div className="bg-white rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          Standar Umum Visualisasi
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {levels.map((level) => {
            const viz = LEVEL_VISUALIZATIONS[level];
            return (
              <div
                key={level}
                className={`p-4 rounded-lg border-2 text-center ${viz.borderColor} ${viz.bgColor}`}
              >
                <div className="text-2xl mb-2">{viz.icon}</div>
                <div className={`text-sm font-semibold mb-1 ${viz.textColor}`}>
                  {level}
                </div>
                <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${viz.color}`}>
                  {level}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Implementasi RIASEC */}
      <div className="bg-white rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          Implementasi RIASEC (Skor 0-25)
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {riasecScores.map((score, index) => {
            const scoreLevel = getRiasecScoreLevel(score);
            return (
              <div
                key={score}
                className="p-4 rounded-lg border-2 border-gray-200 bg-gray-50 text-center"
              >
                <div className="text-lg mb-2">{scoreLevel.icon}</div>
                <div className="text-sm font-semibold mb-1 text-gray-700">
                  Skor: {score}
                </div>
                <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${scoreLevel.color}`}>
                  {scoreLevel.level}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  {scoreLevel.description}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Implementasi OCEAN */}
      <div className="bg-white rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          Implementasi OCEAN (Skor 5-25)
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {oceanScores.map((score, index) => {
            const scoreLevel = getOceanScoreLevel(score);
            return (
              <div
                key={score}
                className="p-4 rounded-lg border-2 border-gray-200 bg-gray-50 text-center"
              >
                <div className="text-lg mb-2">{scoreLevel.icon}</div>
                <div className="text-sm font-semibold mb-1 text-gray-700">
                  Skor: {score}
                </div>
                <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${scoreLevel.color}`}>
                  {scoreLevel.level}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  {scoreLevel.description}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Panduan Penggunaan */}
      <div className="bg-blue-50 rounded-2xl border border-blue-200 p-6">
        <h2 className="text-xl font-semibold text-blue-800 mb-4">
          📋 Panduan Penggunaan
        </h2>
        <div className="space-y-3 text-sm text-blue-700">
          <div>
            <strong>Import:</strong>
            <code className="ml-2 px-2 py-1 bg-blue-100 rounded text-xs">
              import {`{ getRiasecScoreLevel, getOceanScoreLevel }`} from '@/lib/levelVisualization';
            </code>
          </div>
          <div>
            <strong>RIASEC:</strong> Gunakan <code className="px-1 bg-blue-100 rounded">getRiasecScoreLevel(score)</code> untuk skor 0-25
          </div>
          <div>
            <strong>OCEAN:</strong> Gunakan <code className="px-1 bg-blue-100 rounded">getOceanScoreLevel(score)</code> untuk skor 5-25
          </div>
          <div>
            <strong>Return:</strong> Kedua fungsi mengembalikan <code className="px-1 bg-blue-100 rounded">{`{ level, color, description, icon }`}</code>
          </div>
        </div>
      </div>

      {/* Icon Reference */}
      <div className="bg-gray-50 rounded-2xl border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          🎨 Referensi Icon
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
          <div>
            <div className="text-2xl mb-1">❄️</div>
            <div className="text-xs text-gray-600">Sangat Rendah</div>
          </div>
          <div>
            <div className="text-2xl mb-1">💤</div>
            <div className="text-xs text-gray-600">Rendah</div>
          </div>
          <div>
            <div className="text-2xl mb-1">⚡</div>
            <div className="text-xs text-gray-600">Sedang</div>
          </div>
          <div>
            <div className="text-2xl mb-1">🔥</div>
            <div className="text-xs text-gray-600">Tinggi</div>
          </div>
          <div>
            <div className="text-2xl mb-1">🚀</div>
            <div className="text-xs text-gray-600">Sangat Tinggi</div>
          </div>
        </div>
      </div>
    </div>
  );
}
