import type { NextConfig } from "next";
import path from 'path';

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
  },
  // Disable React Strict Mode to prevent double API calls in development
  reactStrictMode: false,

  // Output configuration
  output: 'standalone',

  // Webpack configuration to fix Windows permission issues
  webpack: (config) => {
    // Restrict webpack to only scan the project directory
    config.context = path.resolve(process.cwd());

    // More restrictive file watching
    config.watchOptions = {
      ...config.watchOptions,
      ignored: [
        '**/node_modules/**',
        '**/.git/**',
        '**/.next/**',
        '**/Application Data/**',
        '**/AppData/**',
        '**/ProgramData/**',
        '**/System Volume Information/**',
        '**/$Recycle.Bin/**',
        '**/Windows/**',
        '**/Program Files/**',
      ],
    };

    // Add resolve fallbacks for Node.js modules
    config.resolve = {
      ...config.resolve,
      fallback: {
        ...config.resolve?.fallback,
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        url: false,
      },
    };

    // Optimize for Windows and restrict file system access
    config.optimization = {
      ...config.optimization,
      moduleIds: 'deterministic',
    };

    // Add snapshot options to prevent scanning system directories
    config.snapshot = {
      ...config.snapshot,
      managedPaths: [path.resolve(process.cwd(), 'node_modules')],
      immutablePaths: [],
      buildDependencies: {
        hash: true,
        timestamp: true,
      },
    };

    return config;
  },
};

export default nextConfig;
