import { CombinedProfileInterpretation } from '@/lib/profileStore';

interface ProfileSummaryProps {
  combinedProfile?: CombinedProfileInterpretation | null;
  isCombinedMode?: boolean;
}

export default function ProfileSummary({
  combinedProfile,
  isCombinedMode = false
}: ProfileSummaryProps) {
  // Only use combined profile data
  const data = combinedProfile;

  if (!data) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-xl p-8 mb-8 text-white">
      <div className="text-center mb-6">
        <div className="inline-block bg-white/20 rounded-full p-4 mb-4">
          <span className="text-4xl">{isCombinedMode ? '🧠' : '🎯'}</span>
        </div>
        <h2 className="text-3xl font-bold mb-2">
          {data.profileTitle}
        </h2>
        <p className="text-indigo-100 text-lg leading-relaxed max-w-3xl mx-auto">
          {data.profileDescription}
        </p>
      </div>

      {/* Combined Profile Layout */}
      <div className="grid md:grid-cols-2 gap-6 mb-6">
        {/* Strengths */}
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">💪</span>
              Key Strengths
          </h3>
          <ul className="space-y-2">
            {data.strengths.map((strength: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2 mt-1">✓</span>
                <span className="text-indigo-100">{strength}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Career Suggestions */}
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">🚀</span>
            Career Recommendations
          </h3>
          <ul className="space-y-2">
            {data.careerSuggestions.slice(0, 5).map((career: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2 mt-1">•</span>
                <span className="text-indigo-100">{career}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mb-6">
        {/* Personality Insights */}
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">🧠</span>
            Personality Insights
          </h3>
          <ul className="space-y-2">
            {data.personalityInsights.map((insight: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2 mt-1">💡</span>
                <span className="text-indigo-100">{insight}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Development Areas */}
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">📈</span>
            Development Areas
          </h3>
          <ul className="space-y-2">
            {data.developmentAreas.map((area: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2 mt-1">🎯</span>
                <span className="text-indigo-100">{area}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Work Style & Career Fit */}
      <div className="grid md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-3 flex items-center">
            <span className="mr-2">🏢</span>
            Ideal Work Environment
          </h3>
          <p className="text-indigo-100 leading-relaxed">
            {data.workEnvironment}
          </p>
        </div>

        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-3 flex items-center">
            <span className="mr-2">🎯</span>
            Career Fit
          </h3>
          <p className="text-indigo-100 leading-relaxed">
            {data.careerFit}
          </p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center mt-6">
        <div className="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
          <p className="text-indigo-100 text-sm">
            💡 <strong>Catatan Penting:</strong> Hasil ini menggambarkan minat dan preferensi Anda, bukan kemampuan atau keahlian.
            Gunakan sebagai panduan untuk mengeksplorasi bidang yang menarik bagi Anda dan pertimbangkan untuk mengembangkan keterampilan yang diperlukan!
          </p>
        </div>
      </div>
    </div>
  );
}
