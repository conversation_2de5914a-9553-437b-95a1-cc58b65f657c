const { PrismaClient } = require('../lib/generated/prisma');

const prisma = new PrismaClient();

async function quickClean() {
  console.log('🧹 Quick Database Clean - Removing all assessment data...');
  
  try {
    // Delete in correct order due to foreign key constraints
    console.log('🗑️ Deleting profiles...');
    const profileResult = await prisma.profile.deleteMany({});
    console.log(`✅ Deleted ${profileResult.count} profiles`);
    
    console.log('🗑️ Deleting assessments...');
    const assessmentResult = await prisma.assessment.deleteMany({});
    console.log(`✅ Deleted ${assessmentResult.count} assessments`);
    
    // Keep users for now, only clean assessment data
    console.log('\n✨ Database cleaned! Assessment and profile data removed.');
    console.log('👥 Users were preserved.');
    
  } catch (error) {
    console.error('❌ Error during cleaning:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  quickClean()
    .then(() => {
      console.log('🎉 Quick clean completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Quick clean failed:', error);
      process.exit(1);
    });
}

module.exports = { quickClean };
