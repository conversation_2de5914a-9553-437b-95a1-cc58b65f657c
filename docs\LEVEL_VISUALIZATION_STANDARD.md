# Standar Visualisasi Tingkat

Dokumen ini menjelaskan standar visualisasi tingkat yang digunakan untuk komponen RIASEC dan OCEAN dalam aplikasi talent mapping.

## 🎯 Tujuan

Menyediakan konsistensi visual untuk:
- Icon yang merepresentasikan tingkat
- Skema warna yang konsisten
- Deskripsi yang seragam
- Pengalaman pengguna yang koheren

## 📊 Tingkat Visualisasi

### Standar Umum

| Tingkat | Icon | Warna | Deskripsi |
|---------|------|-------|-----------|
| Sangat Rendah | ❄️ | Slate (abu-abu gelap) | Tingkat yang sangat rendah |
| Rendah | 💤 | Gray (abu-abu) | Tingkat yang rendah |
| Sedang | ⚡ | Yellow (kuning) | Tingkat yang moderat |
| Tinggi | 🔥 | Blue (biru) | Tingkat yang tinggi |
| Sangat Tinggi | 🚀 | Green (hijau) | Tingkat yang sangat tinggi |

### Implementasi RIASEC

**Range Skor:** 0-25 (berdasarkan 5 pertanyaan × skala 1-5)

| Skor | Tingkat | Icon | Deskripsi |
|------|---------|------|-----------|
| 0-4 | Sangat Rendah | ❄️ | Minat yang sangat rendah pada area ini |
| 5-9 | Rendah | 💤 | Minat yang rendah pada area ini |
| 10-14 | Sedang | ⚡ | Minat yang moderat pada area ini |
| 15-19 | Tinggi | 🔥 | Minat yang cukup kuat pada area ini |
| 20-25 | Sangat Tinggi | 🚀 | Minat yang sangat kuat pada area ini |

### Implementasi OCEAN

**Range Skor:** 5-25 (berdasarkan 5 pertanyaan × skala 1-5)

| Skor | Tingkat | Icon | Deskripsi |
|------|---------|------|-----------|
| 5-7 | Sangat Rendah | ❄️ | Tingkat trait yang sangat rendah |
| 8-12 | Rendah | 💤 | Tingkat trait yang rendah |
| 13-18 | Sedang | ⚡ | Tingkat trait yang sedang |
| 19-21 | Tinggi | 🔥 | Tingkat trait yang tinggi |
| 22-25 | Sangat Tinggi | 🚀 | Tingkat trait yang sangat tinggi |

## 🛠️ Penggunaan

### Import

```typescript
import { 
  getRiasecScoreLevel, 
  getOceanScoreLevel,
  getLevelVisualization,
  LEVEL_VISUALIZATIONS 
} from '@/lib/levelVisualization';
```

### Untuk RIASEC

```typescript
const scoreLevel = getRiasecScoreLevel(score);
// Returns: { level, color, description, icon }

// Contoh penggunaan
<div className={`px-2 py-1 rounded ${scoreLevel.color}`}>
  {scoreLevel.icon} {scoreLevel.level}
</div>
```

### Untuk OCEAN

```typescript
const scoreLevel = getOceanScoreLevel(score);
// Returns: { level, color, description, icon }

// Contoh penggunaan
<div className={`px-2 py-1 rounded ${scoreLevel.color}`}>
  {scoreLevel.icon} {scoreLevel.level}
</div>
```

### Akses Langsung Standar

```typescript
import { LEVEL_VISUALIZATIONS } from '@/lib/levelVisualization';

const tinggiViz = LEVEL_VISUALIZATIONS['Tinggi'];
// Returns: { level, icon, color, bgColor, borderColor, textColor, description }
```

## 🎨 Skema Warna

### Tailwind CSS Classes

```css
/* Sangat Rendah - Slate */
.sangat-rendah {
  @apply text-slate-600 bg-slate-50 border-slate-200;
}

/* Rendah - Gray */
.rendah {
  @apply text-gray-600 bg-gray-50 border-gray-200;
}

/* Sedang - Yellow */
.sedang {
  @apply text-yellow-600 bg-yellow-50 border-yellow-200;
}

/* Tinggi - Blue */
.tinggi {
  @apply text-blue-600 bg-blue-50 border-blue-200;
}

/* Sangat Tinggi - Green */
.sangat-tinggi {
  @apply text-green-600 bg-green-50 border-green-200;
}
```

## 📁 File Struktur

```
lib/
├── levelVisualization.ts    # Standar dan utility functions
components/
├── RiasecChart.tsx         # Menggunakan getRiasecScoreLevel()
├── OceanChart.tsx          # Menggunakan getOceanScoreLevel()
└── LevelVisualizationDemo.tsx  # Demo komponen
```

## 🔄 Migrasi dari Standar Lama

### Sebelum (Tidak Konsisten)

```typescript
// RiasecChart.tsx - menggunakan emoji hardcoded
{scoreLevel.level === 'Tinggi' ? '🔥' : scoreLevel.level === 'Sedang' ? '⚡' : '💤'}

// OceanChart.tsx - menggunakan icon mapping terpisah
const icons = { O: "🎨", C: "📋", E: "🎉", A: "🤝", N: "😰" };
```

### Sesudah (Konsisten)

```typescript
// Kedua komponen menggunakan standar yang sama
const scoreLevel = getRiasecScoreLevel(score); // atau getOceanScoreLevel(score)
<div>{scoreLevel.icon}</div>
```

## 🧪 Testing

Untuk melihat demo standar visualisasi:
```
http://localhost:3000/demo-visualization
```

## 📝 Catatan Penting

1. **Konsistensi**: Semua komponen harus menggunakan fungsi dari `levelVisualization.ts`
2. **Backward Compatibility**: Fungsi `getScoreLevel()` di `profileStore.ts` tetap ada untuk kompatibilitas
3. **Extensibility**: Mudah menambah tingkat baru atau mengubah icon/warna
4. **Type Safety**: Semua tingkat menggunakan TypeScript types yang ketat

## 🔮 Future Enhancements

- [ ] Animasi transisi antar tingkat
- [ ] Dark mode support
- [ ] Accessibility improvements (ARIA labels)
- [ ] Internationalization (i18n) support
- [ ] Custom theme support
