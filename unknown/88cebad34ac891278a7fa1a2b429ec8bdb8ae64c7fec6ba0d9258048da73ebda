import { likertOptions } from '@/lib/riasecQuestions';

interface LikertScaleInputProps {
  questionId: number;
  selectedValue?: number;
  onSelect: (questionId: number, value: number) => void;
}

export default function LikertScaleInput({
  questionId,
  selectedValue,
  onSelect
}: LikertScaleInputProps) {
  return (
    <div className="space-y-8">
      <p className="text-center text-gray-700 text-base mb-12">
        Seberapa setuju Anda dengan pernyataan di atas?
      </p>

      {/* Minimalist Likert Scale */}
      <div className="px-8 py-6">
        {/* Clean labels */}
        <div className="flex justify-between items-center mb-10">
          <span className="text-sm text-gray-500">Tidak Setuju</span>
          <span className="text-sm text-gray-500">Setuju</span>
        </div>

        {/* Subtle baseline */}
        <div className="relative mb-8">
          <div className="absolute top-4 left-0 right-0 h-px bg-gray-200"></div>
        </div>

        {/* Elegant radio buttons */}
        <div className="flex justify-between items-center">
          {likertOptions.map((option) => (
            <label
              key={option.value}
              className="flex flex-col items-center cursor-pointer group"
            >
              <div className="relative">
                <input
                  type="radio"
                  name={`question-${questionId}`}
                  value={option.value}
                  checked={selectedValue === option.value}
                  onChange={() => onSelect(questionId, option.value)}
                  className="sr-only"
                />
                
                {/* Minimalist radio button */}
                <div className={`
                  w-8 h-8 rounded-full border transition-all duration-200 flex items-center justify-center
                  ${selectedValue === option.value 
                    ? 'border-gray-900 bg-gray-900' 
                    : 'border-gray-300 bg-white hover:border-gray-400'
                  }
                `}>
                  {selectedValue === option.value && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
              </div>

              {/* Clean number label */}
              <span className={`
                mt-3 text-sm transition-colors duration-200
                ${selectedValue === option.value 
                  ? 'text-gray-900 font-medium' 
                  : 'text-gray-400 group-hover:text-gray-600'
                }
              `}>
                {option.value}
              </span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
}