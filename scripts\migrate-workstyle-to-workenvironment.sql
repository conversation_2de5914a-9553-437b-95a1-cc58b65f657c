-- Migration script to rename workStyle column to workEnvironment
-- This script safely renames the column while preserving existing data

-- For SQLite, we need to recreate the table since ALTER COLUMN is not fully supported
-- Step 1: Create new table with correct schema
CREATE TABLE profiles_new (
    id TEXT PRIMARY KEY,
    assessmentId TEXT UNIQUE NOT NULL,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    profileTitle TEXT NOT NULL,
    profileDescription TEXT NOT NULL,
    strengths TEXT NOT NULL,
    careerSuggestions TEXT NOT NULL,
    workEnvironment TEXT NOT NULL,
    development<PERSON>reas TEXT NOT NULL,
    personalityInsights TEXT NOT NULL,
    careerFit TEXT NOT NULL,
    generatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    aiModel TEXT NOT NULL DEFAULT 'gemini-2.5-pro',
    FOREIGN KEY (assessmentId) REFERENCES assessments(id) ON DELETE CASCADE
);

-- Step 2: Copy data from old table to new table, renaming workStyle to workEnvironment
INSERT INTO profiles_new (
    id, assessmentId, createdAt, updatedAt, profileTitle, profileDescription,
    strengths, careerSuggestions, workEnvironment, developmentAreas,
    personalityInsights, careerFit, generatedAt, aiModel
)
SELECT 
    id, assessmentId, createdAt, updatedAt, profileTitle, profileDescription,
    strengths, careerSuggestions, workStyle, developmentAreas,
    personalityInsights, careerFit, generatedAt, aiModel
FROM profiles;

-- Step 3: Drop old table
DROP TABLE profiles;

-- Step 4: Rename new table to original name
ALTER TABLE profiles_new RENAME TO profiles;

-- Verify the migration
SELECT COUNT(*) as total_profiles FROM profiles;
SELECT id, profileTitle, workEnvironment FROM profiles LIMIT 5;
