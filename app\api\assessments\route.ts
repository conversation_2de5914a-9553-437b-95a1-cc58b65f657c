import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  validateRiasecScores,
  validateOceanScores
} from '@/lib/api-utils';
import { AssessmentRequest, AssessmentResponse } from '@/lib/api-types';
import { db } from '@/lib/server/database';
import { withMiddleware, sanitizeInput } from '@/lib/server/middleware';

async function postHandler(request: NextRequest) {
  const rawBody = await request.json();
  const body: AssessmentRequest = sanitizeInput(rawBody);

  // Validate request body
  if (!body.riasecScores || !body.oceanScores) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Missing riasecScores or oceanScores',
      400
    );
  }

  // Validate RIASEC scores
  if (!validateRiasecScores(body.riasecScores)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid RIASEC scores. Each score must be between 0-30',
      400
    );
  }

  // Validate OCEAN scores
  if (!validateOceanScores(body.oceanScores)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid OCEAN scores. Each score must be between 5-25',
      400
    );
  }

  // Create assessment in database
  const dbAssessment = await db.createAssessment(
    body.riasecScores,
    body.oceanScores,
    body.userId
  );

  const assessment: AssessmentResponse = {
    id: dbAssessment.id,
    riasecScores: body.riasecScores,
    oceanScores: body.oceanScores,
    createdAt: dbAssessment.createdAt.toISOString(),
    profileGenerated: false,
  };

  return createSuccessResponse(assessment);
}

export const POST = withMiddleware(postHandler);

async function getHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const assessmentId = searchParams.get('id');

  if (!assessmentId) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Assessment ID is required',
      400
    );
  }

  // Validate assessment ID format (basic check)
  if (assessmentId.length < 10 || assessmentId.length > 50) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID format',
      400
    );
  }

  const dbAssessment = await db.getAssessmentWithScores(assessmentId);
  if (!dbAssessment) {
    return createErrorResponse(
      'NOT_FOUND',
      'Assessment not found',
      404
    );
  }

  const assessment: AssessmentResponse = {
    id: dbAssessment.id,
    riasecScores: dbAssessment.riasecScores,
    oceanScores: dbAssessment.oceanScores,
    createdAt: dbAssessment.createdAt.toISOString(),
    profileGenerated: !!dbAssessment.profile,
  };

  return createSuccessResponse(assessment);
}

export const GET = withMiddleware(getHandler);
