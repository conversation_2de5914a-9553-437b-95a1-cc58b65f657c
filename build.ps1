# PowerShell script to build Next.js with restricted permissions
# This script attempts to fix Windows permission issues during build

Write-Host "Setting up build environment..." -ForegroundColor Green

# Set environment variables to restrict file system access
$env:NODE_OPTIONS = "--max-old-space-size=4096"
$env:NEXT_TELEMETRY_DISABLED = "1"
$env:CI = "true"

# Ensure we're in the correct directory
Set-Location "d:\(program-projects)\Fullstack-Learning\atma-next"

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow

# Clean previous build
Write-Host "Cleaning previous build..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force ".next" -ErrorAction SilentlyContinue
}

# Run the build
Write-Host "Starting Next.js build..." -ForegroundColor Green
try {
    & npm run build
    Write-Host "Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
