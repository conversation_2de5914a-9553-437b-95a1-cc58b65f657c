import { NextRequest } from 'next/server';
import { createSuccessResponse } from '@/lib/api-utils';
import { ServerGeminiProfileService } from '@/lib/server/geminiService';
import { withMiddleware } from '@/lib/server/middleware';

async function getHandler(request: NextRequest) {
  const geminiService = ServerGeminiProfileService.getInstance();
  const stats = geminiService.getStats();
  
  return createSuccessResponse({
    message: 'Gemini service statistics',
    stats,
    timestamp: new Date().toISOString(),
  });
}

export const GET = withMiddleware(getHandler);
