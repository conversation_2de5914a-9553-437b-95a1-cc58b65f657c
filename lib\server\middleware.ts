import { NextRequest, NextResponse } from 'next/server';
import { createErrorResponse } from '@/lib/api-utils';

// Rate limiting store (in-memory for development)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting middleware
export function rateLimit(maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) {
  return (req: NextRequest) => {
    const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    for (const [key, value] of rateLimitStore.entries()) {
      if (value.resetTime < now) {
        rateLimitStore.delete(key);
      }
    }

    const current = rateLimitStore.get(ip) || { count: 0, resetTime: now + windowMs };

    if (current.resetTime < now) {
      // Reset window
      current.count = 1;
      current.resetTime = now + windowMs;
    } else {
      current.count++;
    }

    rateLimitStore.set(ip, current);

    if (current.count > maxRequests) {
      return createErrorResponse(
        'RATE_LIMIT_EXCEEDED',
        `Too many requests. Limit: ${maxRequests} per ${windowMs / 1000} seconds`,
        429
      );
    }

    return null; // No rate limit hit
  };
}

// Request validation middleware
export function validateContentType(req: NextRequest) {
  if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return createErrorResponse(
        'INVALID_CONTENT_TYPE',
        'Content-Type must be application/json',
        400
      );
    }
  }
  return null;
}

// CORS middleware
export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_APP_URL || '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400',
  };
}

// Error handler wrapper
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);

      if (error instanceof Error) {
        // Handle specific error types
        if (error.message.includes('Prisma')) {
          return createErrorResponse(
            'DATABASE_ERROR',
            'Database operation failed',
            500
          ) as R;
        }

        if (error.message.includes('GEMINI_API_KEY')) {
          return createErrorResponse(
            'CONFIGURATION_ERROR',
            'AI service configuration error',
            500
          ) as R;
        }

        if (error.message.includes('quota') || error.message.includes('rate limit')) {
          return createErrorResponse(
            'EXTERNAL_SERVICE_ERROR',
            'External service rate limit exceeded',
            429
          ) as R;
        }

        if (error.message.includes('timeout')) {
          return createErrorResponse(
            'TIMEOUT_ERROR',
            'Request timeout',
            408
          ) as R;
        }
      }

      return createErrorResponse(
        'INTERNAL_ERROR',
        'An unexpected error occurred',
        500
      ) as R;
    }
  };
}

// Request logging middleware
export function logRequest(req: NextRequest) {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';

  console.log(`[${timestamp}] ${method} ${url} - IP: ${ip} - UA: ${userAgent}`);
}

// Health check for dependencies
export async function healthCheck() {
  const checks = {
    database: false,
    gemini: false,
    timestamp: new Date().toISOString(),
  };

  try {
    // Check database
    const { db } = await import('@/lib/server/database');
    const dbHealth = await db.healthCheck();
    checks.database = dbHealth.status === 'healthy';
  } catch (error) {
    console.error('Database health check failed:', error);
  }

  try {
    // Check Gemini API key
    checks.gemini = !!process.env.GEMINI_API_KEY;
  } catch (error) {
    console.error('Gemini health check failed:', error);
  }

  return checks;
}

// Input sanitization
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    return input.trim().slice(0, 1000); // Limit string length
  }
  
  if (typeof input === 'number') {
    return isNaN(input) ? 0 : Math.max(-1000, Math.min(1000, input)); // Limit number range
  }
  
  if (Array.isArray(input)) {
    return input.slice(0, 100).map(sanitizeInput); // Limit array length
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    const keys = Object.keys(input).slice(0, 50); // Limit object keys
    for (const key of keys) {
      if (typeof key === 'string' && key.length <= 100) {
        sanitized[key] = sanitizeInput(input[key]);
      }
    }
    return sanitized;
  }
  
  return input;
}

// Combine all middleware
export function withMiddleware(handler: (req: NextRequest) => Promise<NextResponse>) {
  return withErrorHandler(async (req: NextRequest) => {
    // Log request
    logRequest(req);

    // Check rate limit
    const rateLimitCheck = rateLimit()(req);
    if (rateLimitCheck) return rateLimitCheck;

    // Validate content type
    const contentTypeCheck = validateContentType(req);
    if (contentTypeCheck) return contentTypeCheck;

    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
      return new NextResponse(null, { status: 200, headers: corsHeaders() });
    }

    // Execute handler
    const response = await handler(req);

    // Add CORS headers to response
    const headers = corsHeaders();
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  });
}
