import { Question, RiasecDescription } from './types';

// RIASEC Career Interest Questions (30 total - 5 per type)
export const riasecQuestions: Question[] = [
  // Realistic (R) - Hands-on, practical, mechanical
  {
    id: 1,
    text: "Saya suka bekerja dengan peralatan, perkakas, atau mesin.",
    riasec_type: 'R'
  },
  {
    id: 7,
    text: "Saya lebih suka bekerja di luar ruangan atau dengan tangan saya secara langsung.",
    riasec_type: 'R'
  },
  {
    id: 13,
    text: "Saya menikmati pekerjaan yang memiliki hasil akhir yang nyata dan dapat disentuh.",
    riasec_type: 'R'
  },
  {
    id: 19,
    text: "Saya terampil dalam memperbaiki atau merakit sesuatu.",
    riasec_type: 'R'
  },
  {
    id: 25,
    text: "Saya tertarik pada karir di bidang teknik, pembangunan, atau pertanian.",
    riasec_type: 'R'
  },

  // Investigative (I) - Analytical, scientific, research-oriented
  {
    id: 2,
    text: "Saya menikmati memecahkan masalah matematika atau sains yang rumit.",
    riasec_type: 'I'
  },
  {
    id: 8,
    text: "Saya sering menghabiskan waktu untuk mempelajari cara kerja suatu hal.",
    riasec_type: 'I'
  },
  {
    id: 14,
    text: "Saya didorong oleh rasa ingin tahu untuk melakukan riset atau penyelidikan.",
    riasec_type: 'I'
  },
  {
    id: 20,
    text: "Saya menikmati proses menganalisis data untuk menemukan pola atau solusi.",
    riasec_type: 'I'
  },
  {
    id: 26,
    text: "Saya mempertimbangkan karir di bidang penelitian, teknologi, atau medis.",
    riasec_type: 'I'
  },

  // Artistic (A) - Creative, expressive, imaginative
  {
    id: 3,
    text: "Saya senang mengekspresikan diri melalui seni, musik, atau tulisan.",
    riasec_type: 'A'
  },
  {
    id: 9,
    text: "Saya memiliki imajinasi yang kuat dan suka berpikir di luar hal yang biasa.",
    riasec_type: 'A'
  },
  {
    id: 15,
    text: "Saya menghargai keindahan dan keaslian dalam berbagai bentuk.",
    riasec_type: 'A'
  },
  {
    id: 21,
    text: "Saya lebih suka lingkungan kerja yang fleksibel dan tidak terlalu terstruktur.",
    riasec_type: 'A'
  },
  {
    id: 27,
    text: "Saya membayangkan diri saya bekerja sebagai seniman, desainer, atau musisi.",
    riasec_type: 'A'
  },

  // Social (S) - Helping, teaching, caring for others
  {
    id: 4,
    text: "Saya merasa ingin membantu, mengajar, atau melayani orang lain.",
    riasec_type: 'S'
  },
  {
    id: 10,
    text: "Bekerja dalam tim untuk mencapai tujuan bersama adalah hal yang memuaskan bagi saya.",
    riasec_type: 'S'
  },
  {
    id: 16,
    text: "Saya pandai memahami perasaan dan sudut pandang orang lain.",
    riasec_type: 'S'
  },
  {
    id: 22,
    text: "Orang lain sering datang kepada saya untuk meminta nasihat atau dukungan.",
    riasec_type: 'S'
  },
  {
    id: 28,
    text: "Karir di bidang konseling, pendidikan, atau pekerjaan sosial terdengar menarik.",
    riasec_type: 'S'
  },

  // Enterprising (E) - Leading, persuading, business-oriented
  {
    id: 5,
    text: "Saya tertarik untuk memimpin sebuah proyek atau bisnis.",
    riasec_type: 'E'
  },
  {
    id: 11,
    text: "Saya suka membujuk atau memengaruhi orang lain untuk mencapai suatu tujuan.",
    riasec_type: 'E'
  },
  {
    id: 17,
    text: "Saya tidak takut mengambil risiko untuk mendapatkan keuntungan atau kesuksesan.",
    riasec_type: 'E'
  },
  {
    id: 23,
    text: "Saya memiliki ambisi untuk mencapai posisi yang penting dan berpengaruh.",
    riasec_type: 'E'
  },
  {
    id: 29,
    text: "Saya cocok dengan dunia penjualan, manajemen, atau politik.",
    riasec_type: 'E'
  },

  // Conventional (C) - Organizing, detail-oriented, systematic
  {
    id: 6,
    text: "Saya menyukai pekerjaan yang terorganisir, rapi, dan mengikuti prosedur yang jelas.",
    riasec_type: 'C'
  },
  {
    id: 12,
    text: "Saya efisien dalam mengelola data, catatan, atau jadwal.",
    riasec_type: 'C'
  },
  {
    id: 18,
    text: "Saya merasa nyaman di lingkungan kerja yang stabil dan bisa diprediksi.",
    riasec_type: 'C'
  },
  {
    id: 24,
    text: "Ketelitian dan perhatian terhadap detail adalah kekuatan saya.",
    riasec_type: 'C'
  },
  {
    id: 30,
    text: "Saya akan berhasil dalam pekerjaan administrasi, akuntansi, atau perbankan.",
    riasec_type: 'C'
  }
];

// RIASEC type descriptions
export const riasecDescriptions: RiasecDescription[] = [
  {
    type: 'R',
    name: 'Realistic',
    description: 'Menyukai pekerjaan yang melibatkan aktivitas fisik, menggunakan alat, mesin, atau bekerja di luar ruangan. Cenderung praktis dan suka bekerja dengan tangan.'
  },
  {
    type: 'I',
    name: 'Investigative',
    description: 'Menyukai pekerjaan yang melibatkan penelitian, analisis, dan pemecahan masalah. Cenderung analitis, intelektual, dan suka bekerja dengan ide-ide.'
  },
  {
    type: 'A',
    name: 'Artistic',
    description: 'Menyukai pekerjaan yang melibatkan kreativitas, ekspresi diri, dan inovasi. Cenderung imajinatif, ekspresif, dan suka bekerja dalam lingkungan yang fleksibel.'
  },
  {
    type: 'S',
    name: 'Social',
    description: 'Menyukai pekerjaan yang melibatkan interaksi dengan orang lain, membantu, mengajar, atau memberikan layanan. Cenderung kooperatif, ramah, dan peduli terhadap kesejahteraan orang lain.'
  },
  {
    type: 'E',
    name: 'Enterprising',
    description: 'Menyukai pekerjaan yang melibatkan kepemimpinan, persuasi, dan pengambilan keputusan bisnis. Cenderung ambisius, percaya diri, dan suka mengambil risiko.'
  },
  {
    type: 'C',
    name: 'Conventional',
    description: 'Menyukai pekerjaan yang melibatkan organisasi, detail, dan prosedur yang terstruktur. Cenderung teratur, teliti, dan suka bekerja dengan data atau sistem.'
  }
];

// Function to shuffle questions randomly
export function getShuffledRiasecQuestions(): Question[] {
  const shuffled = [...riasecQuestions];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Likert scale options for RIASEC
export const riasecLikertOptions = [
  { value: 1, label: 'Sangat Tidak Setuju' },
  { value: 2, label: 'Tidak Setuju' },
  { value: 3, label: 'Netral' },
  { value: 4, label: 'Setuju' },
  { value: 5, label: 'Sangat Setuju' }
];

// Legacy exports for backward compatibility
export const questions = riasecQuestions;
export const getShuffledQuestions = getShuffledRiasecQuestions;
export const likertOptions = riasecLikertOptions;
