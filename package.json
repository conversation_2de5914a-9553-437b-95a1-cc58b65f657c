{"name": "atma-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset", "db:clean": "node scripts/clean-database.js", "db:quick-clean": "node scripts/quick-clean.js", "db:migrate": "node scripts/migrate-database.js", "test:api": "node scripts/test-api.js", "type-check": "tsc --noEmit"}, "dependencies": {"@google/genai": "^1.9.0", "@prisma/client": "^6.11.1", "chart.js": "^4.5.0", "next": "15.3.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}