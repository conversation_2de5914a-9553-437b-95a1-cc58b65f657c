const { PrismaClient } = require('../lib/generated/prisma');
const fs = require('fs');
const path = require('path');

async function migrateDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔄 Starting database migration: workStyle -> workEnvironment');
    
    // Check if migration is needed
    console.log('📊 Checking current database state...');
    
    // Try to query the old column to see if migration is needed
    try {
      const result = await prisma.$queryRaw`SELECT workStyle FROM profiles LIMIT 1`;
      console.log('✅ Found workStyle column, migration needed');
    } catch (error) {
      if (error.message.includes('no such column: workStyle')) {
        console.log('✅ workStyle column not found, checking for workEnvironment...');
        try {
          await prisma.$queryRaw`SELECT workEnvironment FROM profiles LIMIT 1`;
          console.log('✅ Migration already completed - workEnvironment column exists');
          return;
        } catch (e) {
          console.log('❌ Neither workStyle nor workEnvironment found, something is wrong');
          throw e;
        }
      } else {
        throw error;
      }
    }
    
    // Count existing profiles
    const profileCount = await prisma.profile.count();
    console.log(`📈 Found ${profileCount} profiles to migrate`);
    
    if (profileCount === 0) {
      console.log('📝 No data to migrate, running simple schema update...');
      await prisma.$disconnect();
      return;
    }
    
    // Backup existing data
    console.log('💾 Creating backup of existing data...');
    const profiles = await prisma.$queryRaw`SELECT * FROM profiles`;
    fs.writeFileSync(
      path.join(__dirname, 'backup-profiles.json'), 
      JSON.stringify(profiles, null, 2)
    );
    console.log('✅ Backup created: scripts/backup-profiles.json');
    
    // Execute migration SQL
    console.log('🔄 Executing migration...');
    
    // Step 1: Create new table
    await prisma.$executeRaw`
      CREATE TABLE profiles_new (
        id TEXT PRIMARY KEY,
        assessmentId TEXT UNIQUE NOT NULL,
        createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        profileTitle TEXT NOT NULL,
        profileDescription TEXT NOT NULL,
        strengths TEXT NOT NULL,
        careerSuggestions TEXT NOT NULL,
        workEnvironment TEXT NOT NULL,
        developmentAreas TEXT NOT NULL,
        personalityInsights TEXT NOT NULL,
        careerFit TEXT NOT NULL,
        generatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        aiModel TEXT NOT NULL DEFAULT 'gemini-2.5-pro',
        FOREIGN KEY (assessmentId) REFERENCES assessments(id) ON DELETE CASCADE
      )
    `;
    console.log('✅ Created new table structure');
    
    // Step 2: Copy data
    await prisma.$executeRaw`
      INSERT INTO profiles_new (
        id, assessmentId, createdAt, updatedAt, profileTitle, profileDescription,
        strengths, careerSuggestions, workEnvironment, developmentAreas,
        personalityInsights, careerFit, generatedAt, aiModel
      )
      SELECT 
        id, assessmentId, createdAt, updatedAt, profileTitle, profileDescription,
        strengths, careerSuggestions, workStyle, developmentAreas,
        personalityInsights, careerFit, generatedAt, aiModel
      FROM profiles
    `;
    console.log('✅ Copied data to new table');
    
    // Step 3: Drop old table
    await prisma.$executeRaw`DROP TABLE profiles`;
    console.log('✅ Dropped old table');
    
    // Step 4: Rename new table
    await prisma.$executeRaw`ALTER TABLE profiles_new RENAME TO profiles`;
    console.log('✅ Renamed new table');
    
    // Verify migration
    const newCount = await prisma.profile.count();
    console.log(`✅ Migration completed! Verified ${newCount} profiles`);
    
    // Test the new column
    const sample = await prisma.$queryRaw`SELECT id, profileTitle, workEnvironment FROM profiles LIMIT 3`;
    console.log('📋 Sample migrated data:', sample);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration
if (require.main === module) {
  migrateDatabase()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateDatabase };
