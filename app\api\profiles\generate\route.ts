import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  validateRiasecScores,
  validateOceanScores
} from '@/lib/api-utils';
import { ProfileGenerationRequest, ProfileGenerationResponse } from '@/lib/api-types';
import { ServerGeminiProfileService } from '@/lib/server/geminiService';
import { withMiddleware, sanitizeInput } from '@/lib/server/middleware';

async function postHandler(request: NextRequest) {
  const rawBody = await request.json();
  const body: ProfileGenerationRequest = sanitizeInput(rawBody);

  // Validate request body
  if (!body.riasecScores || !body.oceanScores) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Missing riasecScores or oceanScores',
      400
    );
  }

  // Validate RIASEC scores
  if (!validateRiasecScores(body.riasecScores)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid RIASEC scores. Each score must be between 0-30',
      400
    );
  }

  // Validate OCEAN scores
  if (!validateOceanScores(body.oceanScores)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid OCEAN scores. Each score must be between 5-25',
      400
    );
  }

  // Initialize Gemini service with timeout
  const geminiService = ServerGeminiProfileService.getInstance();

  // Generate profile using AI with timeout
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Profile generation timeout')), 30000);
  });

  const aiProfile = await Promise.race([
    geminiService.generateCombinedProfile(body.riasecScores, body.oceanScores),
    timeoutPromise
  ]);

  // Create response
  const response: ProfileGenerationResponse = {
    ...aiProfile as any,
    generatedAt: new Date().toISOString(),
  };

  return createSuccessResponse(response);
}

export const POST = withMiddleware(postHandler);
